# Don Fenticas Booking System Email Setup

This document provides instructions on how to set up the email functionality for the Don Fenticas booking system.

## Overview

The booking system sends two emails when a customer makes a booking:
1. A confirmation email to the customer
2. A notification email to the business

These emails are sent using [EmailJS](https://www.emailjs.com/), a service that allows sending emails directly from client-side JavaScript without requiring a backend server.

## Setup Instructions

### 1. Create an EmailJS Account

1. Go to [EmailJS](https://www.emailjs.com/) and sign up for an account
2. After signing up, log in to your dashboard

### 2. Connect an Email Service

1. In the EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider (Gmail, Outlook, etc.)
4. Follow the instructions to connect your email account
5. Name your service (e.g., "Don Fenticas Booking")
6. Note down the Service ID (you'll need it later)

### 3. Create Email Templates

You need to create two email templates:

#### Customer Confirmation Template

1. In the EmailJS dashboard, go to "Email Templates"
2. Click "Create New Template"
3. Name it "Customer Booking Confirmation"
4. Design your template with the following variables:
   - `{{booking_id}}` - The unique booking ID
   - `{{event_name}}` - The name of the event
   - `{{event_date}}` - The date of the event
   - `{{customer_name}}` - The customer's name
   - `{{number_of_people}}` - Number of people in the booking
   - `{{special_requests}}` - Any special requests
   - `{{company_name}}` - Don Fenticas
   - `{{company_email}}` - Company email
   - `{{company_phone}}` - Company phone
   - `{{company_address}}` - Company address
5. Set the "To email" field to `{{to_email}}`
6. Set an appropriate subject line (e.g., "Your Booking Confirmation - Don Fenticas")
7. Save the template and note down the Template ID

#### Company Notification Template

1. In the EmailJS dashboard, go to "Email Templates"
2. Click "Create New Template"
3. Name it "Company Booking Notification"
4. Design your template with the following variables:
   - `{{booking_id}}` - The unique booking ID
   - `{{event_name}}` - The name of the event
   - `{{event_date}}` - The date of the event
   - `{{customer_name}}` - The customer's name
   - `{{customer_email}}` - The customer's email
   - `{{customer_phone}}` - The customer's phone
   - `{{number_of_people}}` - Number of people in the booking
   - `{{special_requests}}` - Any special requests
5. Set the "To email" field to `{{to_email}}`
6. Set an appropriate subject line (e.g., "New Booking Notification - Don Fenticas")
7. Save the template and note down the Template ID

### 4. Update Configuration in the Website

1. Open the file `src/config/emailConfig.ts`
2. Update the following values:
   - `serviceId`: Your EmailJS Service ID
   - `customerTemplateId`: Your Customer Confirmation Template ID
   - `companyTemplateId`: Your Company Notification Template ID
   - `publicKey`: Your EmailJS Public Key (found in Account > API Keys)
   - `companyEmail`: The email address where booking notifications should be sent
   - Update the company information if needed

```typescript
export const emailConfig = {
  // Your EmailJS service ID
  serviceId: 'your_service_id_here',

  // Template ID for customer confirmation email
  customerTemplateId: 'your_customer_template_id_here',

  // Template ID for company notification email
  companyTemplateId: 'your_company_template_id_here',

  // Your EmailJS public key
  publicKey: 'your_public_key_here',

  // Company email for notifications
  companyEmail: '<EMAIL>',

  // Company contact information (used in customer confirmation emails)
  companyInfo: {
    name: 'Don Fenticas',
    phone: '+44 (0)1455 698767',
    address: '1 Edwards Centre, Regent Street, Hinckley, LE10 0BB'
  }
};
```

## Testing the Email System

After setting up EmailJS and updating the configuration:

1. Run the website locally
2. Navigate to the Events section
3. Click "Book Now" on any event
4. Fill out the booking form and submit
5. Check that both emails are sent successfully:
   - The customer should receive a confirmation email
   - The company email should receive a notification email

## Troubleshooting

If emails are not being sent:

1. Check the browser console for any error messages
2. Verify that all IDs and keys in the configuration are correct
3. Make sure your EmailJS account is active and has available email credits
4. Check if your email service (Gmail, etc.) has any restrictions on sending emails from third-party applications

## Production Considerations

For a production environment:

1. Consider upgrading to a paid EmailJS plan for higher email limits
2. Implement server-side email sending for more security and reliability
3. Add email validation and spam protection measures
