.booking-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--text-color);
}

.form-control {
  padding: 0.75rem;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: var(--border-radius);
  font-family: var(--body-font);
  font-size: 1rem;
  background-color: var(--input-bg);
  color: #333;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-color);
}

.form-control::placeholder {
  color: #999;
}

textarea.form-control {
  min-height: 100px;
  resize: vertical;
}

.form-error {
  color: #e53935;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-family: var(--body-font);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.2s;
}

.btn:hover {
  transform: translateY(-2px);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--button-hover);
}

.btn-secondary {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--text-color);
}

.btn-secondary:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.booking-success {
  text-align: center;
  padding: 2rem 1rem;
}

.booking-success h3 {
  margin-bottom: 1rem;
}

.booking-success p {
  margin-bottom: 1.5rem;
}

/* Day mode adjustments - Green & Yellow Theme */
body[data-theme="day"] .booking-form {
  background-color: var(--form-bg);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  border-left: 4px solid var(--accent-color);
}

body[data-theme="day"] .form-control {
  border: 1px solid var(--secondary-color);
  background-color: var(--input-bg);
}

body[data-theme="day"] .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(38, 48, 13, 0.1);
}

body[data-theme="day"] .btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: 2px solid var(--accent-color);
}

body[data-theme="day"] .btn-primary:hover {
  background-color: var(--accent-color);
  color: var(--primary-color);
}

body[data-theme="day"] .form-group label {
  color: var(--accent-color);
  font-weight: 600;
}

body[data-theme="day"] .booking-success h3 {
  color: var(--accent-color);
  font-weight: 700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* Night mode adjustments - Nightclub/Indie Vibe */
body[data-theme="night"] .form-control {
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--text-color);
  border-color: var(--primary-color);
  border-width: 2px;
}

body[data-theme="night"] .form-control:focus {
  box-shadow: 0 0 0 3px rgba(255, 45, 85, 0.4);
  border-color: var(--primary-color);
}

body[data-theme="night"] .form-control::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

body[data-theme="night"] .btn-secondary {
  background-color: rgba(255, 45, 85, 0.15);
  color: var(--accent-color);
  border: 1px solid var(--accent-color);
}

body[data-theme="night"] .btn-secondary:hover {
  background-color: rgba(255, 45, 85, 0.25);
}
