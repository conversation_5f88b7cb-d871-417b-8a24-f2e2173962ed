import { useContext } from 'react';
import { ThemeContext } from '../context/ThemeContext';
import { dayContent, nightContent } from '../data/content';
import MapWrapper from './MapWrapper';
import './Visit.css';

const Visit = () => {
  const { mode } = useContext(ThemeContext);
  const content = mode === 'day' ? dayContent : nightContent;

  return (
    <section id="visit" className="visit">
      <div className="container">
        <h2 className="text-center">{content.hours.title}</h2>

        <div className="visit-content">
          <div className="hours-card">
            <div className="hours-info">
              <div className="hours-list">
                {content.hours.days.map((dayHours, index) => (
                  <div key={index} className="hours-item">
                    <span className="day">{dayHours.day}</span>
                    <span className="hours">{dayHours.hours}</span>
                  </div>
                ))}
              </div>
              <p className="hours-note">{content.hours.note}</p>
            </div>
          </div>

          <div className="location-card">
            <h3>Find Us</h3>
            <address>
              <span className="address-number">1</span> Edwards Centre<br />
              Regent Street<br />
              Hinckley, <span className="address-number">LE10 0BB</span>
            </address>
            <p className="contact-info">
              <strong>Phone:</strong> <span className="phone-number">+44 (0)1455 698767</span><br />
              <strong>Email:</strong> <EMAIL>
            </p>
            <MapWrapper
              latitude={52.5417}
              longitude={-1.3750}
              zoom={15}
              popupTitle="Don Fenticas"
              popupText="1 Edwards Centre, Regent Street, Hinckley, LE10 0BB"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Visit;
