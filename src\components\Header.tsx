import { useContext } from 'react';
import { ThemeContext } from '../context/ThemeContext';
import ThemeToggle from './ThemeToggle';
import './Header.css';

const Header = () => {
  const { mode } = useContext(ThemeContext);

  return (
    <header className="header">
      <div className="container header-container">
        <div className="logo">
          <h1><PERSON></h1>
          <p className="tagline">{mode === 'day' ? 'Coffee & Community' : 'Drinks & Vibes'}</p>
        </div>

        <nav className="nav">
          <ul>
            <li><a href="#about">About</a></li>
            <li><a href="#menu">Menu</a></li>
            <li><a href="#events">Events</a></li>
            <li><a href="#gallery">Gallery</a></li>
            <li><a href="#visit">Visit Us</a></li>
          </ul>
        </nav>

        <div className="desktop-toggle-container">
          <ThemeToggle />
        </div>
      </div>
    </header>
  );
};

export default Header;
