import { useEffect, useRef } from 'react';
import { useBooking } from '../context/BookingContext';
import { ThemeContext } from '../context/ThemeContext';
import { useContext } from 'react';
import { dayContent, nightContent } from '../data/content';
import BookingForm from './BookingForm';
import './BookingModal.css';

const BookingModal = () => {
  const { bookingState, closeBookingModal } = useBooking();
  const { mode } = useContext(ThemeContext);
  const content = mode === 'day' ? dayContent : nightContent;
  const modalRef = useRef<HTMLDivElement>(null);

  // Find the selected event
  const selectedEvent = bookingState.selectedEventId !== null
    ? content.events.list[bookingState.selectedEventId]
    : null;

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        closeBookingModal();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [closeBookingModal]);

  // Close modal when pressing Escape key
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        closeBookingModal();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [closeBookingModal]);

  // Prevent scrolling of the body when modal is open
  useEffect(() => {
    if (bookingState.isModalOpen) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = 'auto';
      };
    }
  }, [bookingState.isModalOpen]);

  if (!bookingState.isModalOpen || !selectedEvent) {
    return null;
  }

  return (
    <div className="modal-overlay">
      <div className="modal" ref={modalRef}>
        <div className="modal-header">
          <h2 className="modal-title">Book Event: {selectedEvent.title}</h2>
          <button
            className="modal-close"
            onClick={closeBookingModal}
            aria-label="Close booking form"
          >
            Close
          </button>
        </div>
        <div className="modal-body">
          <p>
            <strong>Date:</strong> {selectedEvent.date}<br />
            <strong>Time:</strong> {selectedEvent.time}
          </p>
          <BookingForm event={selectedEvent} eventId={bookingState.selectedEventId} />
        </div>
      </div>
    </div>
  );
};

export default BookingModal;
