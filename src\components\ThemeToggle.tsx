import { useContext } from 'react';
import { ThemeContext } from '../context/ThemeContext';
import { FaSun, FaMoon } from 'react-icons/fa';
import './ThemeToggle.css';

const ThemeToggle = () => {
  const { mode, toggleMode } = useContext(ThemeContext);

  return (
    <button
      className="theme-toggle"
      onClick={toggleMode}
      aria-label={mode === 'day' ? 'Switch to Pub Mode' : 'Switch to Café Mode'}
    >
      {mode === 'day' ? (
        <>
          <FaMoon className="toggle-icon" />
          <strong className="toggle-text">SWITCH TO PUB</strong>
        </>
      ) : (
        <>
          <FaSun className="toggle-icon" />
          <strong className="toggle-text">SWITCH TO CAFÉ</strong>
        </>
      )}
    </button>
  );
};

export default ThemeToggle;
