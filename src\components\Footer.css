.footer {
  background-color: var(--card-bg);
  color: var(--text-color);
  padding: 3rem 0 1.5rem;
  transition: background-color var(--transition-speed), color var(--transition-speed);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.footer-logo h3 {
  margin: 0;
  font-size: 1.8rem;
}

.footer-logo p {
  margin: 0;
  opacity: 0.8;
}

.footer-social {
  display: flex;
  gap: 1.5rem;
}

.footer-social a {
  color: var(--primary-color);
  font-size: 1.5rem;
  transition: color 0.3s, transform 0.3s;
}

.footer-social a:hover {
  color: var(--accent-color);
  transform: translateY(-3px);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  padding-top: 1.5rem;
  border-top: 1px solid var(--secondary-color);
  opacity: 0.8;
  font-size: 0.9rem;
}

.footer-bottom p {
  margin: 0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .footer-content, .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .footer-social {
    justify-content: center;
    margin-top: 1rem;
  }
}
