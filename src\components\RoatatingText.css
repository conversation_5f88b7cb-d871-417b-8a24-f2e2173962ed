.text-rotate {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100%;
  overflow: hidden;
}

.text-rotate-content {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  height: 100%;
  overflow: hidden;
}

.text-rotate-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-rotate-word {
  display: inline-flex;
  align-items: center;
  height: 100%;
  overflow: hidden;
}

.text-rotate-lines {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
}

.text-rotate-element {
  display: inline-block;
  overflow: hidden;
}

.text-rotate-space {
  white-space: pre;
}