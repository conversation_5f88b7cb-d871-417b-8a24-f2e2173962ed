/**
 * EmailJS Configuration
 *
 * This file contains the configuration for EmailJS service.
 * You'll need to replace these values with your actual EmailJS credentials.
 *
 * To set up EmailJS:
 * 1. Create an account at https://www.emailjs.com/
 * 2. Create a service (e.g., Gmail, Outlook, etc.)
 * 3. Create two email templates:
 *    - One for customer confirmation
 *    - One for company notification
 * 4. Update the values below with your service ID, template IDs, and public key
 */

export const emailConfig = {
  // Your EmailJS service ID
  serviceId: 'service_donfenticas',

  // Template ID for customer confirmation email
  customerTemplateId: 'template_customer_booking',

  // Template ID for company notification email
  companyTemplateId: 'template_company_booking',

  // Your EmailJS public key
  publicKey: 'your_public_key',

  // Company email for notifications
  companyEmail: '<EMAIL>',

  // Company contact information (used in customer confirmation emails)
  companyInfo: {
    name: '<PERSON>',
    phone: '+44 (0)1455 698767',
    address: '1 Edwards Centre, Regent Street, Hinckley, LE10 0BB'
  }
};
