import { useEffect } from 'react';
import L from 'leaflet';
import Map from './Map';

// Fix for marker icons in react-leaflet
const fixLeafletIcon = () => {
  delete (L.Icon.Default.prototype as any)._getIconUrl;
  
  L.Icon.Default.mergeOptions({
    iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
    iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
    shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
  });
};

interface MapWrapperProps {
  latitude: number;
  longitude: number;
  zoom: number;
  popupTitle: string;
  popupText: string;
}

const MapWrapper = (props: MapWrapperProps) => {
  useEffect(() => {
    fixLeafletIcon();
  }, []);

  return <Map {...props} />;
};

export default MapWrapper;
