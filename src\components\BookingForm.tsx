import { useState } from 'react';
import type { FormEvent } from 'react';
import { useBooking } from '../context/BookingContext';
import type { BookingFormData } from '../types/booking';
import type { Event } from '../data/content';
import './BookingForm.css';

interface BookingFormProps {
  event: Event;
  eventId: number | null;
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  numberOfPeople?: string;
}

const BookingForm = ({ event, eventId }: BookingFormProps) => {
  const { bookingState, submitBooking, resetBookingForm } = useBooking();
  const [formData, setFormData] = useState<Omit<BookingFormData, 'eventId' | 'date'>>({
    name: '',
    email: '',
    phone: '',
    numberOfPeople: 1,
    specialRequests: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
      isValid = false;
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
      isValid = false;
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
      isValid = false;
    }

    if (formData.numberOfPeople < 1) {
      newErrors.numberOfPeople = 'Number of people must be at least 1';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'numberOfPeople' ? parseInt(value) : value
    }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm() || eventId === null) {
      return;
    }

    const bookingData: BookingFormData = {
      ...formData,
      eventId,
      date: event.date
    };

    await submitBooking(bookingData, event.title);
  };

  if (bookingState.isSubmitted) {
    return (
      <div className="booking-success">
        <h3>Booking Confirmed!</h3>
        <p>Thank you for booking with Don Fenticas. We've sent a confirmation email to {formData.email}.</p>
        <p>A notification has also been sent to our team, and they will contact you if needed.</p>
        <p>We look forward to seeing you at {event.title}!</p>
        <button
          className="btn btn-primary"
          onClick={resetBookingForm}
        >
          Book Another Event
        </button>
      </div>
    );
  }

  return (
    <form className="booking-form" onSubmit={handleSubmit}>
      <div className="form-group">
        <label htmlFor="name">Full Name</label>
        <input
          type="text"
          id="name"
          name="name"
          className="form-control"
          value={formData.name}
          onChange={handleChange}
          placeholder="Enter your full name"
          required
        />
        {errors.name && <div className="form-error">{errors.name}</div>}
      </div>

      <div className="form-group">
        <label htmlFor="email">Email</label>
        <input
          type="email"
          id="email"
          name="email"
          className="form-control"
          value={formData.email}
          onChange={handleChange}
          placeholder="Enter your email"
          required
        />
        {errors.email && <div className="form-error">{errors.email}</div>}
      </div>

      <div className="form-group">
        <label htmlFor="phone">Phone Number</label>
        <input
          type="tel"
          id="phone"
          name="phone"
          className="form-control"
          value={formData.phone}
          onChange={handleChange}
          placeholder="Enter your phone number"
          required
        />
        {errors.phone && <div className="form-error">{errors.phone}</div>}
      </div>

      <div className="form-group">
        <label htmlFor="numberOfPeople">Number of People</label>
        <input
          type="number"
          id="numberOfPeople"
          name="numberOfPeople"
          className="form-control"
          value={formData.numberOfPeople}
          onChange={handleChange}
          min="1"
          max="20"
          required
        />
        {errors.numberOfPeople && <div className="form-error">{errors.numberOfPeople}</div>}
      </div>

      <div className="form-group">
        <label htmlFor="specialRequests">Special Requests (Optional)</label>
        <textarea
          id="specialRequests"
          name="specialRequests"
          className="form-control"
          value={formData.specialRequests}
          onChange={handleChange}
          placeholder="Any special requests or accommodations"
        />
      </div>

      <div className="form-actions">
        <button
          type="submit"
          className="btn btn-primary"
          disabled={bookingState.isSubmitting}
        >
          {bookingState.isSubmitting ? 'Submitting...' : 'Book Now'}
        </button>
      </div>

      {bookingState.error && (
        <div className="form-error">{bookingState.error}</div>
      )}
    </form>
  );
};

export default BookingForm;
