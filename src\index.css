/* Base styles and theme variables */
:root {
  /* Common variables */
  --transition-speed: 0.5s;
  --border-radius: 8px;
  --max-width: 1200px;
  --header-height: 80px;

  /* Font settings */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Day Theme (Café) - Green & Yellow Theme */
body[data-theme="day"] {
  --primary-color: #3a4a22;
  --secondary-color: #5a6b3c;
  --accent-color: #e6c068;
  --background-color: #3f4a2f;
  --alt-background-color: #4a5639;
  --text-color: #ffffff;
  --card-bg: #4d5b38;
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --heading-font: 'Playfair Display', serif;
  --body-font: 'Raleway', sans-serif;
  --button-hover: #e6c068;
  --section-padding: 5rem 0;
  --border-color: rgba(230, 192, 104, 0.3);
  --accent-bg-color: rgba(230, 192, 104, 0.15);
  --light-green-bg: #465234;
  --menu-item-bg: #4d5b38;
  --form-bg: #4d5b38;
  --input-bg: #3f4a2f;
  --text-on-dark: #ffffff;
  --text-on-accent: #3a4a22;
  --warm-overlay: rgba(230, 192, 104, 0.05);
}

/* Night Theme (Nightclub Vibe) */
body[data-theme="night"] {
  --primary-color: #FF2D55;
  --secondary-color: #B01C3E;
  --accent-color: #00F5FF;
  --background-color: #0A0A0F;
  --alt-background-color: #151520;
  --text-color: #FFFFFF;
  --card-bg: #1A1A25;
  --card-shadow: 0 0 20px rgba(255, 45, 85, 0.3), 0 0 40px rgba(0, 245, 255, 0.1);
  --heading-font: 'Playfair Display', serif;
  --body-font: 'Raleway', sans-serif;
  --button-hover: #FF1A45;
  --section-padding: 5rem 0;
  --border-color: rgba(255, 255, 255, 0.1);
  --accent-bg-color: rgba(0, 245, 255, 0.05);
  --menu-item-bg: #1A1A25;
  --form-bg: #1A1A25;
  --input-bg: #0A0A0F;
  --text-on-dark: #FFFFFF;
  --text-on-accent: #0A0A0F;
  --warm-overlay: rgba(255, 45, 85, 0.05);
}

/* Global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Default body styles (fallback) */
body {
  font-family: 'Raleway', sans-serif;
  color: #ffffff;
  background-color: #3f4a2f;
  transition: background-color var(--transition-speed), color var(--transition-speed);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Apply theme-specific styles when data-theme is set */
body[data-theme] {
  font-family: var(--body-font);
  color: var(--text-color);
  background-color: var(--background-color);
}

/* Special styles for day mode (Green & Yellow Theme) */
body[data-theme="day"] a {
  color: var(--accent-color);
}

body[data-theme="day"] a:hover {
  color: #ffffff;
  text-shadow: 0 0 3px var(--accent-color);
}

body[data-theme="day"] .button {
  background-color: var(--primary-color);
  color: #ffffff;
  border: 1px solid var(--accent-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

body[data-theme="day"] .button:hover {
  background-color: var(--accent-color);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

body[data-theme="day"] h2::after {
  content: "";
  display: block;
  width: 60px;
  height: 2px;
  background-color: var(--accent-color);
  margin: 0.5rem auto 1.5rem;
  opacity: 0.8;
}

/* Alternating section backgrounds for day mode */
body[data-theme="day"] section:nth-of-type(odd) {
  background-color: var(--background-color);
}

body[data-theme="day"] section:nth-of-type(even) {
  background-color: var(--alt-background-color);
}

/* Special accent background for specific sections */
body[data-theme="day"] section.menu {
  background-color: var(--light-green-bg);
}

body[data-theme="day"] section.gallery {
  background-color: var(--accent-bg-color);
}

body[data-theme="day"] section.about {
  background: linear-gradient(to bottom, var(--background-color), var(--alt-background-color));
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--heading-font);
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-color);
  transition: color var(--transition-speed);
  line-height: 1.2;
}

body[data-theme="day"] h1,
body[data-theme="day"] h2,
body[data-theme="day"] h3,
body[data-theme="day"] h4,
body[data-theme="day"] h5,
body[data-theme="day"] h6 {
  color: #ffffff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.15);
}

/* Alternating section backgrounds for night mode */
body[data-theme="night"] section:nth-of-type(odd) {
  background-color: var(--background-color);
}

body[data-theme="night"] section:nth-of-type(even) {
  background-color: var(--alt-background-color);
}

/* Special accent background for specific sections */
body[data-theme="night"] section.menu {
  background-color: var(--card-bg);
}

body[data-theme="night"] section.gallery {
  background-color: var(--accent-bg-color);
}

body[data-theme="night"] section.about {
  background: linear-gradient(to bottom, var(--background-color), var(--alt-background-color));
}

h1 {
  font-size: 3rem;
  line-height: 1.2;
}

h2 {
  font-size: 2.5rem;
  line-height: 1.3;
}

h3 {
  font-size: 2rem;
  line-height: 1.4;
}

p {
  margin-bottom: 1.5rem;
  line-height: 1.6;
  color: var(--text-color);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: color var(--transition-speed);
  font-weight: 600;
}

a:hover {
  color: #ffffff;
  text-shadow: 0 0 3px var(--accent-color);
}

button, .button {
  background-color: var(--primary-color);
  color: #ffffff;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: var(--border-radius);
  font-family: var(--body-font);
  font-weight: 600;
  font-size: 0.95rem;
  letter-spacing: 0.5px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

button:hover, .button:hover {
  background-color: var(--accent-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

section {
  padding: var(--section-padding);
  padding-left: 1rem;
  padding-right: 1rem;
}

.container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 1rem;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.mb-1 {
  margin-bottom: 1rem;
}

.mb-2 {
  margin-bottom: 2rem;
}

.mb-3 {
  margin-bottom: 3rem;
}

.mt-1 {
  margin-top: 1rem;
}

.mt-2 {
  margin-top: 2rem;
}

.mt-3 {
  margin-top: 3rem;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  section {
    padding-top: 4rem;
    padding-bottom: 4rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Ensure theme colors are properly applied on tablets */
  body[data-theme="day"] {
    background-color: #3f4a2f;
  }

  body[data-theme="day"] .button {
    padding: 0.7rem 1.3rem;
    font-size: 0.9rem;
  }

  body[data-theme="night"] {
    background-color: #1E1A1D;
  }
}

@media screen and (max-width: 480px) {
  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.75rem;
  }

  h3 {
    font-size: 1.25rem;
  }

  section {
    padding-top: 3rem;
    padding-bottom: 3rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  button, .button {
    padding: 0.7rem 1.2rem;
    font-size: 0.9rem;
  }

  /* Ensure theme colors are properly applied on mobile */
  body[data-theme="day"] {
    background-color: #3f4a2f !important;
  }

  body[data-theme="day"] h2::after {
    width: 50px;
    margin: 0.4rem auto 1.2rem;
  }

  body[data-theme="night"] {
    background-color: #1E1A1D !important;
  }
}
