.gallery {
  background-color: var(--accent-bg-color);
  transition: background-color var(--transition-speed);
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 3rem;
}

.gallery-item {
  position: relative;
  border-radius: var(--border-radius);
  overflow: hidden;
  aspect-ratio: 4/3;
  box-shadow: var(--card-shadow);
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.gallery-item:hover {
  transform: scale(1.03);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 1rem;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.gallery-item:hover .gallery-overlay {
  transform: translateY(0);
}

.gallery-overlay p {
  margin: 0;
  text-align: center;
  font-weight: 500;
}

/* Day mode specific styles - Green & Yellow Theme */
body[data-theme="day"] .gallery-overlay {
  background-color: rgba(38, 48, 13, 0.8);
}

body[data-theme="day"] .gallery-overlay p {
  color: var(--accent-color);
  font-weight: 600;
}

body[data-theme="day"] .gallery-item {
  box-shadow: 0 4px 12px rgba(38, 48, 13, 0.15);
  background-color: var(--card-bg);
}

body[data-theme="day"] .gallery-item:hover {
  box-shadow: 0 8px 20px rgba(38, 48, 13, 0.25);
}

/* Night mode specific styles */
body[data-theme="night"] .gallery {
  background-color: rgba(10, 10, 15, 0.8);
}

body[data-theme="night"] .gallery h2 {
  color: var(--primary-color);
  text-shadow: 0 0 15px rgba(255, 45, 85, 0.7);
  animation: galleryTitleGlow 4s infinite alternate;
}

@keyframes galleryTitleGlow {
  0% {
    text-shadow: 0 0 15px rgba(255, 45, 85, 0.7);
  }
  100% {
    text-shadow: 0 0 25px rgba(255, 45, 85, 0.9);
  }
}

body[data-theme="night"] .gallery-subtitle {
  color: var(--accent-color);
  text-shadow: 0 0 10px rgba(0, 245, 255, 0.7);
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

body[data-theme="night"] .gallery-overlay {
  background-color: rgba(255, 45, 85, 0.8);
}

body[data-theme="night"] .gallery-overlay p {
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
}

body[data-theme="night"] .gallery-item {
  box-shadow: 0 0 15px rgba(255, 45, 85, 0.3), 0 0 30px rgba(0, 245, 255, 0.1);
  animation: galleryItemGlow 3s infinite alternate;
}

@keyframes galleryItemGlow {
  0% {
    box-shadow: 0 0 15px rgba(255, 45, 85, 0.3), 0 0 30px rgba(0, 245, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 20px rgba(255, 45, 85, 0.5), 0 0 40px rgba(0, 245, 255, 0.2);
  }
}

body[data-theme="night"] .gallery-item:hover {
  box-shadow: 0 0 25px rgba(255, 45, 85, 0.6), 0 0 50px rgba(0, 245, 255, 0.3);
}

body[data-theme="night"] .gallery-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  border: 2px solid var(--primary-color);
  border-radius: var(--border-radius);
  opacity: 0;
  transition: opacity 0.3s ease;
}

body[data-theme="night"] .gallery-item:hover .gallery-glow {
  opacity: 1;
  animation: borderGlow 1.5s infinite alternate;
}

@keyframes borderGlow {
  0% {
    box-shadow: 0 0 5px var(--primary-color), inset 0 0 5px var(--primary-color);
  }
  100% {
    box-shadow: 0 0 10px var(--primary-color), inset 0 0 10px var(--primary-color);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
}

/* Lightbox styles */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  cursor: pointer;
}

.lightbox-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
}

.lightbox-image {
  max-width: 100%;
  max-height: 80vh;
  display: block;
  border-radius: 4px;
}

.lightbox-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 24px;
  cursor: pointer;
  z-index: 2001;
  transition: background-color 0.3s;
}

.lightbox-close:hover {
  background: rgba(255, 0, 0, 0.7);
}

.lightbox-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 30px;
  cursor: pointer;
  z-index: 2001;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lightbox-prev {
  left: 10px;
}

.lightbox-next {
  right: 10px;
}

.lightbox-nav:hover {
  background: rgba(255, 255, 255, 0.3);
}

.lightbox-caption {
  position: absolute;
  bottom: -40px;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  text-align: center;
  border-radius: 0 0 4px 4px;
}

/* Night mode lightbox styles */
.lightbox-overlay.night-mode .lightbox-close {
  background: rgba(255, 45, 85, 0.5);
  box-shadow: 0 0 10px rgba(255, 45, 85, 0.5);
}

.lightbox-overlay.night-mode .lightbox-close:hover {
  background: rgba(255, 45, 85, 0.8);
  box-shadow: 0 0 15px rgba(255, 45, 85, 0.7);
}

.lightbox-overlay.night-mode .lightbox-nav {
  background: rgba(0, 245, 255, 0.3);
  box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
}

.lightbox-overlay.night-mode .lightbox-nav:hover {
  background: rgba(0, 245, 255, 0.5);
  box-shadow: 0 0 15px rgba(0, 245, 255, 0.5);
}

.lightbox-overlay.night-mode .lightbox-caption {
  background: linear-gradient(90deg, rgba(255, 45, 85, 0.7), rgba(0, 245, 255, 0.7));
  color: white;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.lightbox-overlay.night-mode .lightbox-image {
  box-shadow: 0 0 30px rgba(255, 45, 85, 0.5), 0 0 60px rgba(0, 245, 255, 0.3);
}
