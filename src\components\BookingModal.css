.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.modal {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  transition: background-color var(--transition-speed), box-shadow var(--transition-speed);
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-color);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-color);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.modal-close:hover {
  opacity: 1;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* Day mode specific styles - Green & Yellow Theme */
body[data-theme="day"] .modal {
  border-top: 4px solid var(--accent-color);
  background-color: var(--form-bg);
  border-left: 4px solid var(--accent-color);
}

body[data-theme="day"] .modal-title {
  color: var(--accent-color);
  font-weight: 700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

body[data-theme="day"] .modal-close {
  background-color: var(--primary-color);
  color: white;
  border: 2px solid var(--accent-color);
  border-radius: var(--border-radius);
  padding: 0.2rem 0.5rem;
  font-size: 1.2rem;
  opacity: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

body[data-theme="day"] .modal-close:hover {
  background-color: var(--accent-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
}

/* Night mode specific styles - Nightclub/Indie Vibe */
body[data-theme="night"] .modal {
  border: none;
  background: linear-gradient(135deg, #1E1E1E 0%, #121212 100%);
  box-shadow: 0 0 20px rgba(255, 45, 85, 0.4), 0 0 40px rgba(255, 45, 85, 0.2);
  position: relative;
  overflow: hidden;
}

body[data-theme="night"] .modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

body[data-theme="night"] .modal-title {
  color: var(--primary-color);
  text-shadow: 0 0 10px rgba(255, 45, 85, 0.3);
}

body[data-theme="night"] .modal-close {
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--text-color);
  border: 2px solid var(--primary-color);
  border-radius: var(--border-radius);
  padding: 0.2rem 0.5rem;
  font-size: 1.2rem;
  opacity: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

body[data-theme="night"] .modal-close:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* Responsive styles */
@media (max-width: 576px) {
  .modal {
    max-width: 100%;
    margin: 0 1rem;
  }

  .modal-header, .modal-body, .modal-footer {
    padding: 1rem;
  }
}
