import emailjs from '@emailjs/browser';
import { emailConfig } from '../config/emailConfig';

/**
 * Sends booking confirmation emails to both the customer and the company
 * @param bookingData The booking data from the form
 * @returns Promise that resolves when both emails are sent
 */
export const sendBookingEmails = async (bookingData: any) => {
  try {
    // Prepare data for company notification email
    const companyEmailParams = {
      booking_id: bookingData.id,
      event_name: bookingData.eventName,
      event_date: bookingData.date,
      customer_name: bookingData.name,
      customer_email: bookingData.email,
      customer_phone: bookingData.phone,
      number_of_people: bookingData.numberOfPeople,
      special_requests: bookingData.specialRequests || 'None',
      to_email: emailConfig.companyEmail,
    };

    // Prepare data for customer confirmation email
    const customerEmailParams = {
      booking_id: bookingData.id,
      event_name: bookingData.eventName,
      event_date: bookingData.date,
      customer_name: bookingData.name,
      number_of_people: bookingData.numberOfPeople,
      special_requests: bookingData.specialRequests || 'None',
      to_email: bookingData.email,
      company_name: emailConfig.companyInfo.name,
      company_email: emailConfig.companyEmail,
      company_phone: emailConfig.companyInfo.phone,
      company_address: emailConfig.companyInfo.address,
    };

    // Initialize EmailJS with your public key
    emailjs.init(emailConfig.publicKey);

    // Send both emails in parallel
    const [companyEmailResult, customerEmailResult] = await Promise.all([
      emailjs.send(
        emailConfig.serviceId,
        emailConfig.companyTemplateId,
        companyEmailParams
      ),
      emailjs.send(
        emailConfig.serviceId,
        emailConfig.customerTemplateId,
        customerEmailParams
      ),
    ]);

    console.log('SUCCESS - Company email sent:', companyEmailResult.status, companyEmailResult.text);
    console.log('SUCCESS - Customer email sent:', customerEmailResult.status, customerEmailResult.text);

    return {
      success: true,
      companyEmailResult,
      customerEmailResult,
    };
  } catch (error) {
    console.error('FAILED to send emails:', error);
    throw error;
  }
};
