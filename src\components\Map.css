.map-container {
  width: 100%;
  height: 250px;
  border-radius: var(--border-radius);
  overflow: hidden;
  z-index: 1;
}

.leaflet-container {
  width: 100%;
  height: 100%;
}

.map-marker-popup .leaflet-popup-content-wrapper {
  background-color: var(--card-bg);
  color: var(--text-color);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  transition: background-color var(--transition-speed), box-shadow var(--transition-speed);
}

.map-marker-popup .leaflet-popup-tip {
  background-color: var(--card-bg);
  transition: background-color var(--transition-speed);
}

.map-marker-popup .leaflet-popup-content {
  margin: 0.75rem 1rem;
  font-family: var(--body-font);
}

.map-marker-popup h4 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-family: var(--heading-font);
}

.map-marker-popup p {
  margin: 0;
  font-size: 0.9rem;
}

/* Day/Night mode specific styles */
body[data-theme="day"] .leaflet-control-attribution a {
  color: var(--primary-color);
}

body[data-theme="day"] .map-marker-popup h4 {
  color: var(--accent-color);
  font-weight: 700;
}

body[data-theme="day"] .map-marker-popup .leaflet-popup-content-wrapper {
  border-top: 3px solid var(--accent-color);
}

body[data-theme="night"] .leaflet-control-attribution a {
  color: var(--primary-color);
}
