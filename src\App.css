.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

/* Animation for theme transition */
.app * {
  transition-property: background-color, color, border-color, box-shadow;
  transition-duration: var(--transition-speed);
  transition-timing-function: ease;
}

/* Scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Accessibility focus styles */
:focus {
  outline: 3px solid var(--primary-color);
  outline-offset: 2px;
}

/* Skip to content link for accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background: var(--primary-color);
  color: white;
  padding: 8px;
  z-index: 1001;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 0;
}
