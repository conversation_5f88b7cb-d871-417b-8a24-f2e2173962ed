import { useContext } from 'react';
import { ThemeContext } from '../context/ThemeContext';
import { dayContent, nightContent } from '../data/content';
import RotatingText from './RotatingText';
import './Hero.css';

const Hero = () => {
  const { mode } = useContext(ThemeContext);
  const content = mode === 'day' ? dayContent : nightContent;

  // Define rotating text based on mode
  const rotatingTexts = mode === 'day'
    ? ['Coffee & Community', 'Fresh Brews Daily', 'Your Local Retreat', 'Artisan Coffee']
    : ['Drinks & Vibes', 'Live Music Nights', 'Craft Cocktails', 'Indie Atmosphere'];

  return (
    <section
      className="hero"
      style={{
        backgroundImage: `url(${content.hero.backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      <div className="hero-overlay"></div>
      <div className="container hero-content">
        <h1>{content.hero.title}</h1>
        <div className="hero-subtitle-container">
          <RotatingText
            texts={rotatingTexts}
            rotationInterval={3000}
            mainClassName="hero-rotating-text"
            splitBy="words"
          />
        </div>
        <a href="#menu" className="button hero-button">
          {mode === 'day' ? 'View Café Menu' : 'View Pub Menu'}
        </a>
      </div>
    </section>
  );
};

export default Hero;
