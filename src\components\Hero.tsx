import { useContext } from 'react';
import { ThemeContext } from '../context/ThemeContext';
import { dayContent, nightContent } from '../data/content';
import './Hero.css';

const Hero = () => {
  const { mode } = useContext(ThemeContext);
  const content = mode === 'day' ? dayContent : nightContent;

  return (
    <section
      className="hero"
      style={{
        backgroundImage: `url(${content.hero.backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      <div className="hero-overlay"></div>
      <div className="container hero-content">
        <h1>{content.hero.title}</h1>
        <p className="hero-subtitle">{content.hero.subtitle}</p>
        <a href="#menu" className="button hero-button">
          {mode === 'day' ? 'View Café Menu' : 'View Pub Menu'}
        </a>
      </div>
    </section>
  );
};

export default Hero;
