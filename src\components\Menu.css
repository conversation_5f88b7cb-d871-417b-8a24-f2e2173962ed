.menu {
  background-color: var(--light-green-bg);
  transition: background-color var(--transition-speed);
}

.menu-subtitle {
  font-size: 1.2rem;
  color: var(--text-color);
  opacity: 0.8;
}

.menu-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
}

.category-button {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: 0.5rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  transition: all var(--transition-speed);
  margin: 0.25rem;
  cursor: pointer;
}

.category-button:hover, .category-button.active {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.menu-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.menu-item {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: transform 0.3s, box-shadow 0.3s, background-color var(--transition-speed);
  position: relative;
}

.menu-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.menu-item-details {
  padding: 1.5rem;
}

.menu-item-name {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.menu-item-price {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.menu-item-description {
  margin-bottom: 1rem;
  line-height: 1.5;
}

/* Responsive styles */
@media (max-width: 768px) {
  .menu-items {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .menu-items {
    grid-template-columns: 1fr;
  }

  .category-button {
    font-size: 0.9rem;
    padding: 0.4rem 1.2rem;
  }
}

/* Day mode specific styles - Green & Yellow Theme */
body[data-theme="day"] .category-button {
  border-color: var(--accent-color);
  color: var(--accent-color);
  background-color: transparent;
}

body[data-theme="day"] .category-button:hover,
body[data-theme="day"] .category-button.active {
  background-color: var(--accent-color);
  color: var(--primary-color);
  box-shadow: 0 0 10px rgba(230, 192, 104, 0.3);
}

body[data-theme="day"] .menu-item {
  border: none;
  box-shadow: 0 4px 12px rgba(38, 48, 13, 0.1);
  position: relative;
  overflow: hidden;
}

body[data-theme="day"] .menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--accent-color);
}

body[data-theme="day"] .menu-item-details {
  background-color: var(--card-bg);
  border-left: 4px solid var(--accent-color);
}

body[data-theme="day"] .menu-item-name {
  color: var(--accent-color);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

body[data-theme="day"] .menu-item-price {
  color: var(--accent-color);
  background-color: var(--primary-color);
  display: inline-block;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  margin-right: 0.5rem;
  font-weight: 700;
}

body[data-theme="day"] .menu-item:hover {
  box-shadow: 0 8px 16px rgba(38, 48, 13, 0.15);
}

/* Night mode specific styles - Pub Theme */
body[data-theme="night"] .category-button {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background-color: transparent;
}

body[data-theme="night"] .category-button:hover,
body[data-theme="night"] .category-button.active {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 0 10px rgba(212, 77, 92, 0.3);
}

body[data-theme="night"] .menu-item {
  border: none;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(255, 45, 85, 0.4), 0 0 40px rgba(0, 245, 255, 0.2);
  background: linear-gradient(135deg, #1A1A25 0%, #0A0A0F 100%);
  position: relative;
  overflow: hidden;
  animation: neonPulse 3s infinite alternate;
}

@keyframes neonPulse {
  0% {
    box-shadow: 0 0 20px rgba(255, 45, 85, 0.4), 0 0 40px rgba(0, 245, 255, 0.2);
  }
  100% {
    box-shadow: 0 0 25px rgba(255, 45, 85, 0.6), 0 0 50px rgba(0, 245, 255, 0.3);
  }
}

body[data-theme="night"] .menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  animation: neonBorder 3s infinite alternate;
}

@keyframes neonBorder {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

body[data-theme="night"] .menu-item-name {
  color: var(--primary-color);
  text-shadow: 0 0 10px rgba(255, 45, 85, 0.7);
  font-weight: 700;
}

body[data-theme="night"] .menu-item-price {
  color: var(--accent-color);
  font-weight: 700;
  display: inline-block;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  margin-right: 0.5rem;
  text-shadow: 0 0 10px rgba(0, 245, 255, 0.7);
  border: 1px solid var(--accent-color);
}

body[data-theme="night"] .menu-item-description {
  color: var(--text-color);
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

body[data-theme="night"] .menu-item:hover {
  box-shadow: 0 0 30px rgba(255, 45, 85, 0.6), 0 0 60px rgba(0, 245, 255, 0.3);
  transform: translateY(-8px);
}
