import { useContext, useState } from 'react';
import { ThemeContext } from '../context/ThemeContext';
import { dayContent, nightContent } from '../data/content';
import './Menu.css';

const Menu = () => {
  const { mode } = useContext(ThemeContext);
  const content = mode === 'day' ? dayContent : nightContent;

  // Get unique categories from menu items
  const categories = [...new Set(content.menu.items.map(item => item.category))];
  const [activeCategory, setActiveCategory] = useState(categories[0]);

  // Filter items by active category
  const filteredItems = content.menu.items.filter(item => item.category === activeCategory);

  return (
    <section id="menu" className="menu">
      <div className="container">
        <h2 className="text-center">{content.menu.title}</h2>
        <p className="menu-subtitle text-center mb-3">{content.menu.subtitle}</p>

        <div className="menu-categories">
          {categories.map(category => (
            <button
              key={category}
              className={`category-button ${activeCategory === category ? 'active' : ''}`}
              onClick={() => setActiveCategory(category)}
            >
              {category}
            </button>
          ))}
        </div>

        <div className="menu-items">
          {filteredItems.map((item, index) => (
            <div key={index} className="menu-item">
              <div className="menu-item-details">
                <h3 className="menu-item-name">{item.name}</h3>
                <p className="menu-item-price">{item.price}</p>
                <p className="menu-item-description">
                  {item.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Menu;
