import { useContext, useState } from 'react';
import { ThemeContext } from '../context/ThemeContext';
import { dayContent, nightContent } from '../data/content';
import CircularGallery from './CircularGallery';
import './Gallery.css';

// Enhanced lightbox component with navigation
const SimpleLightbox = ({
  src,
  alt,
  onClose,
  onPrev,
  onNext
}: {
  src: string;
  alt: string;
  onClose: () => void;
  onPrev?: () => void;
  onNext?: () => void;
}) => {
  const { mode } = useContext(ThemeContext);
  const isNightMode = mode === 'night';

  return (
    <div
      className={`lightbox-overlay ${isNightMode ? 'night-mode' : ''}`}
      onClick={onClose}
    >
      <div
        className="lightbox-container"
        onClick={e => e.stopPropagation()}
      >
        <button
          className="lightbox-close"
          onClick={onClose}
          aria-label="Close"
        >
          ×
        </button>

        {onPrev && (
          <button
            className="lightbox-nav lightbox-prev"
            onClick={(e) => {
              e.stopPropagation();
              onPrev();
            }}
            aria-label="Previous"
          >
            ‹
          </button>
        )}

        <img
          src={src}
          alt={alt}
          className="lightbox-image"
        />

        {onNext && (
          <button
            className="lightbox-nav lightbox-next"
            onClick={(e) => {
              e.stopPropagation();
              onNext();
            }}
            aria-label="Next"
          >
            ›
          </button>
        )}

        <div className="lightbox-caption">
          {alt}
        </div>
      </div>
    </div>
  );
};

const Gallery = () => {
  const { mode } = useContext(ThemeContext);
  const content = mode === 'day' ? dayContent : nightContent;
  const isNightMode = mode === 'night';
  const [selectedImage, setSelectedImage] = useState<{src: string; alt: string} | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [viewMode, setViewMode] = useState<'circular' | 'grid'>('circular');

  const openLightbox = (image: {src: string; alt: string}, index: number) => {
    setSelectedImage(image);
    setCurrentIndex(index);
  };

  const handlePrev = () => {
    const newIndex = (currentIndex - 1 + content.gallery.images.length) % content.gallery.images.length;
    setCurrentIndex(newIndex);
    setSelectedImage(content.gallery.images[newIndex]);
  };

  const handleNext = () => {
    const newIndex = (currentIndex + 1) % content.gallery.images.length;
    setCurrentIndex(newIndex);
    setSelectedImage(content.gallery.images[newIndex]);
  };

  // Convert gallery images to CircularGallery format
  const circularGalleryItems = content.gallery.images.map(image => ({
    image: image.src,
    text: image.alt
  }));

  return (
    <section id="gallery" className={`gallery ${isNightMode ? 'night-mode' : ''}`}>
      <div className="container">
        <h2 className="text-center">{content.gallery.title}</h2>
        <p className="gallery-subtitle text-center">Explore our space through these images</p>

        {/* View Mode Toggle */}
        <div className="gallery-controls">
          <button
            className={`view-toggle ${viewMode === 'circular' ? 'active' : ''}`}
            onClick={() => setViewMode('circular')}
          >
            3D Gallery
          </button>
          <button
            className={`view-toggle ${viewMode === 'grid' ? 'active' : ''}`}
            onClick={() => setViewMode('grid')}
          >
            Grid View
          </button>
        </div>

        {viewMode === 'circular' ? (
          <div className="circular-gallery-container">
            <CircularGallery
              items={circularGalleryItems}
              bend={isNightMode ? 4 : 2}
              textColor={isNightMode ? "#00f5ff" : "#ffffff"}
              borderRadius={0.05}
              font={isNightMode ? "bold 24px Arial" : "bold 20px Arial"}
              scrollSpeed={isNightMode ? 3 : 2}
              scrollEase={0.08}
            />
          </div>
        ) : (
          <div className="gallery-grid">
            {content.gallery.images.map((image, index) => (
              <div
                key={index}
                className="gallery-item"
                onClick={() => openLightbox(image, index)}
              >
                <img src={image.src} alt={image.alt} />
                <div className="gallery-overlay">
                  <p>{image.alt}</p>
                </div>
                {isNightMode && <div className="gallery-glow"></div>}
              </div>
            ))}
          </div>
        )}

        {selectedImage && (
          <SimpleLightbox
            src={selectedImage.src}
            alt={selectedImage.alt}
            onClose={() => setSelectedImage(null)}
            onPrev={handlePrev}
            onNext={handleNext}
          />
        )}
      </div>
    </section>
  );
};

export default Gallery;
