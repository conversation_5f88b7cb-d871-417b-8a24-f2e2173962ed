import { useContext, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Pop<PERSON> } from 'react-leaflet';
import { Icon } from 'leaflet';
import { ThemeContext } from '../context/ThemeContext';
import 'leaflet/dist/leaflet.css';
import './Map.css';

interface MapProps {
  latitude: number;
  longitude: number;
  zoom: number;
  popupTitle: string;
  popupText: string;
}

const Map = ({ latitude, longitude, zoom, popupTitle, popupText }: MapProps) => {
  const { mode } = useContext(ThemeContext);

  // Create a custom icon for the marker
  const customIcon = new Icon({
    iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
    shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41]
  });

  // Update the map tiles when the theme changes
  useEffect(() => {
    const mapContainer = document.querySelector('.leaflet-container');
    if (mapContainer) {
      mapContainer.classList.remove('day-theme', 'night-theme');
      mapContainer.classList.add(`${mode}-theme`);
    }
  }, [mode]);

  return (
    <div className="map-container">
      <MapContainer
        center={[latitude, longitude]}
        zoom={zoom}
        scrollWheelZoom={false}
        className={`${mode}-theme`}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url={mode === 'night'
            ? 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png'
            : 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
          }
        />
        <Marker position={[latitude, longitude]} icon={customIcon}>
          <Popup className="map-marker-popup">
            <h4>{popupTitle}</h4>
            <p>{popupText}</p>
          </Popup>
        </Marker>
      </MapContainer>
    </div>
  );
};

export default Map;
