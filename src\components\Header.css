.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--header-height);
  background-color: var(--primary-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  transition: background-color var(--transition-speed), box-shadow var(--transition-speed);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.logo {
  display: flex;
  flex-direction: column;
}

.logo h1 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  line-height:0;
  color: rgb(143, 0, 0);
}

.tagline {
  font-size: 0.9rem;
  margin: 0;
  opacity: 0.8;
  color: white;
}

.nav ul {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
}

.desktop-toggle-container {
  margin-left: 1.5rem;
  display: flex !important;
  align-items: center;
  visibility: visible !important;
}

.nav a {
  font-weight: 600;
  position: relative;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
  padding: 0.5rem 0;
  transition: color 0.3s ease;
}

.nav a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--accent-color);
  transition: width 0.3s ease;
}

.nav a:hover {
  color: white;
}

.nav a:hover::after {
  width: 100%;
}


/* Day mode specific styles - Green & Yellow Theme */
body[data-theme="day"] .header {
  background-color: var(--primary-color);
}

body[data-theme="day"] .logo h1 {
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

body[data-theme="day"] .tagline {
  color: var(--accent-color);
}

body[data-theme="day"] .nav a {
  color: rgba(255, 255, 255, 0.9);
}

body[data-theme="day"] .nav a:hover {
  color: var(--accent-color);
}

body[data-theme="day"] .nav a::after {
  background-color: var(--accent-color);
}

/* Theme toggle button styles moved to ThemeToggle.css */

/* Night mode specific styles - Nightclub Theme */
body[data-theme="night"] .header {
  background-color: rgba(23, 23, 44, 0.9);
  border-bottom: 1px solid var(--primary-color);
  backdrop-filter: blur(10px);
}

body[data-theme="night"] .logo h1 {
  color: var(--primary-color);
  text-shadow: 0 0 15px rgba(255, 45, 85, 0.7);
  font-weight: 700;
  animation: neonPulse 3s infinite alternate;
}

@keyframes neonPulse {
  0% {
    text-shadow: 0 0 10px rgba(255, 45, 85, 0.7), 0 0 20px rgba(255, 45, 85, 0.5);
  }
  100% {
    text-shadow: 0 0 15px rgba(255, 45, 85, 0.9), 0 0 30px rgba(255, 45, 85, 0.7);
  }
}

body[data-theme="night"] .tagline {
  color: var(--accent-color);
  text-shadow: 0 0 10px rgba(0, 245, 255, 0.7);
}

body[data-theme="night"] .nav a {
  color: white;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

body[data-theme="night"] .nav a:hover {
  color: var(--accent-color);
  text-shadow: 0 0 10px rgba(0, 245, 255, 0.7);
  transform: scale(1.05);
}

body[data-theme="night"] .nav a::after {
  background-color: var(--accent-color);
  box-shadow: 0 0 10px rgba(0, 245, 255, 0.7);
  height: 3px;
}

/* Theme toggle button styles moved to ThemeToggle.css */

/* Responsive styles */
@media (max-width: 768px) {
  .nav {
    display: none;
  }

  /* Theme toggle button styles moved to ThemeToggle.css */
}

@media (max-width: 480px) {
  .logo h1 {
    font-size: 1.5rem;
  }

  .tagline {
    font-size: 0.8rem;
  }

  /* Theme toggle button styles moved to ThemeToggle.css */
}
