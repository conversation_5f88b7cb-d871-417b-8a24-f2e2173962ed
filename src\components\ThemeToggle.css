.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.6rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.85rem;
  letter-spacing: 0.8px;
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 1001;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite;
}

/* Desktop toggle only - mobile toggle is in MobileThemeToggle.css */

/* Style for the toggle in the header */
.desktop-toggle-container .theme-toggle {
  min-width: 140px;
  text-align: center;
  margin-left: 1.5rem;
  border: none;
}

/* Day mode styles */
body[data-theme="day"] .theme-toggle {
  background-color: var(--accent-color);
  color: var(--primary-color);
  border: none;
  box-shadow: 0 2px 8px rgba(230, 192, 104, 0.3);
}

body[data-theme="day"] .theme-toggle:hover {
  background-color: var(--accent-color);
  color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(230, 192, 104, 0.5);
  transform: translateY(-1px);
}

body[data-theme="day"] .toggle-text {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* Night mode styles */
body[data-theme="night"] .theme-toggle {
  background-color: var(--primary-color);
  color: white;
  border: none;
  box-shadow: 0 0 15px rgba(255, 45, 85, 0.4);
}

body[data-theme="night"] .theme-toggle:hover {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 0 20px rgba(255, 45, 85, 0.6);
  transform: translateY(-1px);
}

body[data-theme="night"] .toggle-text {
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.toggle-text {
  display: inline-block !important;
  font-weight: 600 !important;
  font-size: 0.9rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  color: inherit !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-family: var(--body-font) !important;
}

/* Animation */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Desktop toggle now shows on all screen sizes */
