import { createContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';

type ThemeMode = 'day' | 'night';

interface ThemeContextType {
  mode: ThemeMode;
  toggleMode: () => void;
}

export const ThemeContext = createContext<ThemeContextType>({
  mode: 'day',
  toggleMode: () => {},
});

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider = ({ children }: ThemeProviderProps) => {
  // Check localStorage for saved theme, default to 'day'
  const [mode, setMode] = useState<ThemeMode>(() => {
    try {
      const savedMode = localStorage.getItem('themeMode');
      return (savedMode === 'day' || savedMode === 'night') ? savedMode as ThemeMode : 'day';
    } catch (error) {
      return 'day';
    }
  });

  // Set initial theme on body
  useEffect(() => {
    document.body.setAttribute('data-theme', mode);
  }, []);

  // Update localStorage and theme-color meta tag when mode changes
  useEffect(() => {
    try {
      // Update localStorage
      localStorage.setItem('themeMode', mode);

      // Update body attribute
      document.body.setAttribute('data-theme', mode);

      // Update theme-color meta tag for mobile browsers
      const themeColor = mode === 'day' ? '#3f4a2f' : '#1E1A1D';
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');

      if (metaThemeColor) {
        metaThemeColor.setAttribute('content', themeColor);
      } else {
        const newMeta = document.createElement('meta');
        newMeta.name = 'theme-color';
        newMeta.content = themeColor;
        document.head.appendChild(newMeta);
      }
    } catch (error) {
      console.error('Error setting theme:', error);
    }
  }, [mode]);

  const toggleMode = () => {
    setMode(prevMode => prevMode === 'day' ? 'night' : 'day');
  };

  return (
    <ThemeContext.Provider value={{ mode, toggleMode }}>
      {children}
    </ThemeContext.Provider>
  );
};
