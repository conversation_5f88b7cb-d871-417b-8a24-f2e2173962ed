.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 1rem;
  cursor: pointer;
}

.lightbox-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  background-color: transparent;
  border-radius: 4px;
  overflow: hidden;
  cursor: default;
}

.lightbox-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  display: block;
  margin: 0 auto;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
}

.lightbox-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  pointer-events: none;
}

.lightbox-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 1rem;
  text-align: center;
}

.lightbox-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  font-size: 2rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2001;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.lightbox-close:hover {
  opacity: 1;
}

.lightbox-nav {
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  font-size: 2rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: auto;
  opacity: 0.7;
  transition: opacity 0.3s;
  margin: 0 10px;
}

.lightbox-nav:hover {
  opacity: 1;
}

/* Day/Night mode specific styles */
body[data-theme="day"] .lightbox-close,
body[data-theme="day"] .lightbox-nav {
  color: white;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

body[data-theme="night"] .lightbox-close,
body[data-theme="night"] .lightbox-nav {
  color: white;
  text-shadow: 0 0 5px rgba(255, 45, 85, 0.5);
}

/* Responsive styles */
@media (max-width: 768px) {
  .lightbox-nav {
    font-size: 1.5rem;
  }

  .lightbox-close {
    font-size: 1.5rem;
  }
}
