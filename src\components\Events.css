.events {
  background-color: var(--background-color);
  transition: background-color var(--transition-speed);
}

.events-subtitle {
  font-size: 1.2rem;
  color: var(--text-color);
  opacity: 0.8;
}

.events-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.event-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: transform 0.3s, box-shadow 0.3s, background-color var(--transition-speed);
  position: relative;
}

.event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.event-details {
  padding: 1.5rem;
}

.event-title {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.event-date, .event-time {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.event-description {
  margin-bottom: 1rem;
  line-height: 1.5;
}

.book-btn {
  display: inline-block;
  padding: 0.5rem 1.25rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.2s;
}

.book-btn:hover {
  background-color: var(--button-hover);
  transform: translateY(-2px);
}

/* Day mode specific styles - Green & Yellow Theme */
body[data-theme="day"] .event-card {
  border: none;
  box-shadow: 0 4px 12px rgba(38, 48, 13, 0.1);
  position: relative;
  overflow: hidden;
}

body[data-theme="day"] .event-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--accent-color);
}

body[data-theme="day"] .event-details {
  background-color: var(--card-bg);
  border-left: 4px solid var(--accent-color);
}

body[data-theme="day"] .event-title {
  color: var(--accent-color);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

body[data-theme="day"] .event-date,
body[data-theme="day"] .event-time {
  color: var(--accent-color);
  background-color: var(--primary-color);
  display: inline-block;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  margin-right: 0.5rem;
  font-weight: 700;
}

body[data-theme="day"] .book-btn {
  background-color: var(--primary-color);
  color: white;
  transition: all 0.3s ease;
}

body[data-theme="day"] .book-btn:hover {
  background-color: var(--accent-color);
  color: var(--primary-color);
}

/* Night mode specific styles - Nightclub Vibe */
body[data-theme="night"] .event-card {
  border: none;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(255, 45, 85, 0.4), 0 0 40px rgba(0, 245, 255, 0.2);
  background: linear-gradient(135deg, #1A1A25 0%, #0A0A0F 100%);
  position: relative;
  overflow: hidden;
  animation: eventPulse 4s infinite alternate;
}

@keyframes eventPulse {
  0% {
    box-shadow: 0 0 20px rgba(255, 45, 85, 0.4), 0 0 40px rgba(0, 245, 255, 0.2);
  }
  100% {
    box-shadow: 0 0 30px rgba(255, 45, 85, 0.6), 0 0 60px rgba(0, 245, 255, 0.3);
  }
}

body[data-theme="night"] .event-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  animation: eventBorder 3s infinite alternate;
}

@keyframes eventBorder {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

body[data-theme="night"] .event-title {
  color: var(--primary-color);
  text-shadow: 0 0 15px rgba(255, 45, 85, 0.7);
  font-weight: 700;
  letter-spacing: 1px;
}

body[data-theme="night"] .event-date,
body[data-theme="night"] .event-time {
  color: var(--accent-color);
  text-shadow: 0 0 10px rgba(0, 245, 255, 0.7);
  font-weight: 700;
  display: inline-block;
  padding: 0.3rem 0.7rem;
  border-radius: 4px;
  margin-right: 0.5rem;
  border: 1px solid var(--accent-color);
  background-color: rgba(0, 245, 255, 0.1);
}

body[data-theme="night"] .event-description {
  color: var(--text-color);
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

body[data-theme="night"] .book-btn {
  background-color: var(--primary-color);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 15px rgba(255, 45, 85, 0.5);
}

body[data-theme="night"] .book-btn:hover {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 0 20px rgba(255, 45, 85, 0.7);
  text-shadow: 0 0 10px rgba(255, 45, 85, 0.7);
}

/* Responsive styles */
@media (max-width: 768px) {
  .events-list {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .events-list {
    grid-template-columns: 1fr;
  }
}
