.hero {
  height: 100vh;
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding-top: var(--header-height);
  transition: all var(--transition-speed);
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  color: white;
  max-width: 800px;
}

.hero h1 {
  font-size: 4rem;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-title {
  font-size: 4rem;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  text-align: center;
}

.hero-barName {
  font-family: 'Pirata One', cursive;
}

.barname-day {
  color: #e6c068;
  text-shadow: 0px 0px 30px #5a7a22;
}

.barname-night {
  color:#00f5ff;
  text-shadow: 0px 0px 30px #df0a0a ;
}

.hero-plain-text {
  font-family: inherit;
}




.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  min-height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Day mode rotating text styling */
.rotating-text-day {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1.25rem 1.5rem;
  background: rgba(38, 48, 13, 0.9);
  color: #fdcc44;
  border-radius: 8px;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(253, 204, 68, 0.5);
  border: 2px solid rgba(253, 204, 68, 0.3);
  backdrop-filter: blur(10px);
  height: 4rem;
  font-size: 1.1rem;
  position: relative;
  overflow: hidden;
  line-height: 1.2;
}

/* Night mode rotating text styling */
.rotating-text-night {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1.25rem 1.5rem;
  background: rgba(255, 45, 85, 0.2);
  color: #00f5ff;
  border-radius: 8px;
  font-weight: 600;
  text-shadow: 0 0 15px rgba(0, 245, 255, 0.7);
  border: 2px solid rgba(0, 245, 255, 0.3);
  backdrop-filter: blur(10px);
  height: 4rem;
  font-size: 1.1rem;
  position: relative;
  overflow: hidden;
  line-height: 1.2;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .rotating-text-day,
  .rotating-text-night {
    padding: 1.1rem 1.25rem;
    font-size: 1.05rem;
    height: 3.75rem;
    overflow: hidden;
  }
}

@media (max-width: 768px) {
  .rotating-text-day,
  .rotating-text-night {
    padding: 1rem 1rem;
    font-size: 1rem;
    height: 3.5rem;
    overflow: hidden;
  }
}

@media (max-width: 480px) {
  .rotating-text-day,
  .rotating-text-night {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    height: 3rem;
    overflow: hidden;
  }
}

.hero-subtitle-container {
  margin-top: -0.5rem;
  margin-bottom: 5px;
  min-height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-rotating-text {
  font-size: 1.5rem;
  font-weight: 600;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  color: white;
}

.hero-button {
  font-size: 1.1rem;
  padding: 1rem 2rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  margin-top: 1.5rem;
}

.mode-hint {
  margin-top: 2rem;
  font-size: 1rem;
  opacity: 0.9;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  animation: fadeInOut 5s infinite;
}

.hint-highlight {
  font-weight: 700;
  color: var(--accent-color);
}

@keyframes fadeInOut {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* Day mode specific styles - Green & Yellow Theme */
body[data-theme="day"] .hero-overlay {
  background-color: rgba(38, 48, 13, 0.5);
}

/* Night mode specific styles - Nightclub Vibe */
body[data-theme="night"] .hero-overlay {
  background: linear-gradient(135deg, rgba(10, 10, 15, 0.7) 0%, rgba(255, 45, 85, 0.3) 100%);
  animation: pulseOverlay 8s infinite alternate;
}

@keyframes pulseOverlay {
  0% {
    background: linear-gradient(135deg, rgba(10, 10, 15, 0.7) 0%, rgba(255, 45, 85, 0.3) 100%);
  }
  100% {
    background: linear-gradient(135deg, rgba(10, 10, 15, 0.7) 0%, rgba(0, 245, 255, 0.3) 100%);
  }
}

body[data-theme="night"] .hero h1 {
  color: var(--primary-color);
  text-shadow: 0 0 20px rgba(255, 45, 85, 0.8);
  animation: textGlow 4s infinite alternate;
}

@keyframes textGlow {
  0% {
    text-shadow: 0 0 20px rgba(255, 45, 85, 0.8);
  }
  100% {
    text-shadow: 0 0 30px rgba(255, 45, 85, 1);
  }
}

body[data-theme="night"] .hero-subtitle {
  color: var(--accent-color);
  text-shadow: 0 0 15px rgba(0, 245, 255, 0.8);
}

body[data-theme="night"] .hero-rotating-text {
  color: var(--accent-color);
  text-shadow: 0 0 15px rgba(0, 245, 255, 0.8);
  animation: textGlow 4s infinite alternate;
}

body[data-theme="night"] .hero-button {
  background-color: var(--primary-color);
  color: white;
  border: 2px solid var(--primary-color);
  box-shadow: 0 0 20px rgba(255, 45, 85, 0.5);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

body[data-theme="night"] .hero-button:hover {
  background-color: transparent;
  color: var(--primary-color);
  box-shadow: 0 0 30px rgba(255, 45, 85, 0.7);
}

body[data-theme="night"] .hint-highlight {
  color: var(--accent-color);
  text-shadow: 0 0 10px rgba(0, 245, 255, 0.8);
}

/* Responsive styles */
@media (max-width: 768px) {
  .hero h1 {
    font-size: 3rem;
  }

  .hero-subtitle {
    font-size: 1.25rem;
  }

  .mode-hint {
    font-size: 0.95rem;
    margin-top: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero h1 {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-button {
    font-size: 1rem;
    padding: 0.8rem 1.5rem;
  }

  .mode-hint {
    font-size: 0.9rem;
    margin-top: 1.2rem;
    padding: 0.5rem;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 5px;
  }

  .hint-highlight {
    font-size: 1rem;
  }
}