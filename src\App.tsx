import { useEffect } from 'react';
import { ThemeProvider } from './context/ThemeContext';
import { BookingProvider } from './context/BookingContext';
import Header from './components/Header';
import Hero from './components/Hero';
import About from './components/About';
import Menu from './components/Menu';
import Events from './components/Events';
import Gallery from './components/Gallery';
import Visit from './components/Visit';
import Footer from './components/Footer';
import BookingModal from './components/BookingModal';
import './App.css';

function App() {
  // Scroll to section when clicking on navigation links
  useEffect(() => {
    const handleNavClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'A' && target.getAttribute('href')?.startsWith('#')) {
        e.preventDefault();
        const id = target.getAttribute('href')?.substring(1);
        const element = document.getElementById(id || '');
        if (element) {
          const headerHeight = 80; // Match the header height from CSS
          const elementPosition = element.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.scrollY - headerHeight;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          });
        }
      }
    };

    document.addEventListener('click', handleNavClick);
    return () => document.removeEventListener('click', handleNavClick);
  }, []);

  return (
    <ThemeProvider>
      <BookingProvider>
        <div className="app">
          <Header />
          <main>
            <Hero />
            <About />
            <Menu />
            <Events />
            <Gallery />
            <Visit />
          </main>
          <Footer />
          <BookingModal />
        </div>
      </BookingProvider>
    </ThemeProvider>
  );
}

export default App;
