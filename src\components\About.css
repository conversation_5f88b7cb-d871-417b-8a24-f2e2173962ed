.about {
  background: linear-gradient(to bottom, var(--background-color), var(--alt-background-color));
  transition: background-color var(--transition-speed);
  padding: var(--section-padding);
}

.about-content {
  display: flex;
  gap: 2rem;
  align-items: center;
  margin-top: 3rem;
}

.about-text {
  flex: 1;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  transition: transform 0.3s, box-shadow 0.3s, background-color var(--transition-speed);
  position: relative;
  overflow: hidden;
}

.about-text-details {
  padding: 1.5rem;
}

.about-title {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.about-description {
  margin-bottom: 1rem;
  line-height: 1.5;
}

.about-image {
  flex: 1;
}

.about-image img {
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  width: 100%;
  height: auto;
  object-fit: cover;
}

/* Day mode specific styles */
body[data-theme="day"] .about-text {
  background-color: var(--card-bg);
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(38, 48, 13, 0.1);
  position: relative;
  overflow: hidden;
}

body[data-theme="day"] .about-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--accent-color);
}

body[data-theme="day"] .about-text {
  border-left: 4px solid var(--accent-color);
}

body[data-theme="day"] .about-text h2 {
  color: var(--accent-color);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

body[data-theme="day"] .about-text p {
  color: var(--text-color);
}

body[data-theme="day"] .about-text p strong {
  color: var(--primary-color);
}

body[data-theme="day"] .about-image img {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(38, 48, 13, 0.1);
}

body[data-theme="day"] .about-text:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(38, 48, 13, 0.15);
}

/* Night mode specific styles */
body[data-theme="night"] .about-text {
  background: linear-gradient(135deg, #1A1A25 0%, #0A0A0F 100%);
  border: none;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(255, 45, 85, 0.4), 0 0 40px rgba(0, 245, 255, 0.2);
  position: relative;
  overflow: hidden;
  animation: aboutPulse 3s infinite alternate;
}

@keyframes aboutPulse {
  0% {
    box-shadow: 0 0 20px rgba(255, 45, 85, 0.4), 0 0 40px rgba(0, 245, 255, 0.2);
  }
  100% {
    box-shadow: 0 0 25px rgba(255, 45, 85, 0.6), 0 0 50px rgba(0, 245, 255, 0.3);
  }
}

body[data-theme="night"] .about-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  animation: aboutBorder 3s infinite alternate;
}

@keyframes aboutBorder {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

body[data-theme="night"] .about h2 {
  color: var(--primary-color);
  text-shadow: 0 0 15px rgba(255, 45, 85, 0.7);
  font-weight: 700;
  animation: titleGlow 4s infinite alternate;
}

@keyframes titleGlow {
  0% {
    text-shadow: 0 0 15px rgba(255, 45, 85, 0.7);
  }
  100% {
    text-shadow: 0 0 25px rgba(255, 45, 85, 0.9);
  }
}

body[data-theme="night"] .about-title {
  color: var(--primary-color);
  text-shadow: 0 0 10px rgba(255, 45, 85, 0.7);
  font-weight: 700;
}

body[data-theme="night"] .about-text p {
  color: var(--text-color);
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

body[data-theme="night"] .about-image img {
  border: none;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(255, 45, 85, 0.3), 0 0 30px rgba(0, 245, 255, 0.1);
  transition: all 0.3s ease;
}

body[data-theme="night"] .about-text:hover {
  transform: translateY(-5px);
  box-shadow: 0 0 30px rgba(255, 45, 85, 0.6), 0 0 60px rgba(0, 245, 255, 0.3);
}

/* Responsive styles */
@media (max-width: 768px) {
  .about-content {
    flex-direction: column-reverse;
    gap: 2rem;
  }

  .about-text, .about-image {
    width: 100%;
  }

  .about-text:hover {
    transform: translateY(-5px);
  }
}

@media (max-width: 480px) {
  .about-content {
    gap: 1.5rem;
  }

  .about-text-details {
    padding: 1.2rem;
  }

  .about-title {
    font-size: 1.3rem;
  }
}
