import { useContext } from 'react';
import { ThemeContext } from '../context/ThemeContext';
import { dayContent, nightContent } from '../data/content';
import './About.css';

const About = () => {
  const { mode } = useContext(ThemeContext);
  const content = mode === 'day' ? dayContent : nightContent;
  const isNightMode = mode === 'night';

  return (
    <section id="about" className={`about ${isNightMode ? 'night-mode' : ''}`}>
      <div className="container">
        <h2 className="text-center">Our Story</h2>
        <div className="about-content">
          <div className="about-text">
            <div className="about-text-details">
              <h3 className="about-title">{content.about.title}</h3>
              <p className="about-description">
                {content.about.content}
              </p>
            </div>
          </div>
          <div className="about-image">
            <img src={content.about.image} alt={content.about.title} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
