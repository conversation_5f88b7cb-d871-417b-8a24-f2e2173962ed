// Define the booking form data interface
export interface BookingFormData {
  name: string;
  email: string;
  phone: string;
  numberOfPeople: number;
  specialRequests?: string;
  eventId: number;
  date: string;
}

// Define the booking interface that extends the form data
export interface Booking extends BookingFormData {
  id: string;
  createdAt: string;
  status: 'confirmed' | 'pending' | 'cancelled';
}

// Define the booking state interface
export interface BookingState {
  bookings: Booking[];
  isModalOpen: boolean;
  selectedEventId: number | null;
  isSubmitting: boolean;
  isSubmitted: boolean;
  error: string | null;
}
