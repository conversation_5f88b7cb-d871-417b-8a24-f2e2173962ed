.visit {
  background-color: var(--background-color);
  transition: background-color var(--transition-speed);
}

.visit-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

.hours-card, .location-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--card-shadow);
  transition: background-color var(--transition-speed), box-shadow var(--transition-speed);
  position: relative;
  overflow: hidden;
}

.hours-info {
  text-align: center;
}

.hours-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.hours-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.hours-item:last-child {
  border-bottom: none;
}

.day {
  font-weight: 600;
  font-size: 1.1rem;
}

.hours {
  font-size: 1rem;
}

.hours-note {
  font-style: italic;
  opacity: 0.8;
  margin-top: 1rem;
}

.location-card h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
}

address {
  font-style: normal;
  margin-bottom: 1.5rem;
  line-height: 1.6;
  font-size: 1.1rem;
  font-weight: 500;
}

address .address-number {
  font-weight: 600;
  color: rgb(255, 255, 255);
}

.contact-info {
  margin-bottom: 1.5rem;
  line-height: 1.6;
  font-size: 1.1rem;
}

.contact-info .phone-number {
  font-family: var(--heading-font);
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Map styles are in Map.css */

/* Day mode specific styles - Green & Yellow Theme */
body[data-theme="day"] .hours-card::before,
body[data-theme="day"] .location-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--accent-color);
}

body[data-theme="day"] .hours-card {
  border-left: 4px solid var(--accent-color);
}

body[data-theme="day"] .location-card {
  border-left: 4px solid var(--accent-color);
}

body[data-theme="day"] .day {
  color: var(--accent-color);
  font-weight: 700;
  background-color: var(--primary-color);
  display: inline-block;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
}

body[data-theme="day"] .hours {
  color: var(--accent-color);
  font-weight: 700;
  background-color: var(--primary-color);
  display: inline-block;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
}

body[data-theme="day"] .hours-item {
  border-bottom: 1px solid var(--border-color);
}

body[data-theme="day"] .contact-info strong {
  color: var(--accent-color);
  font-weight: 700;
  background-color: var(--primary-color);
  display: inline-block;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  margin-right: 0.5rem;
}

body[data-theme="day"] .contact-info .phone-number {
  color: var(--accent-color);
  background-color: var(--primary-color);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-weight: 700;
  display: inline-block;
}

body[data-theme="day"] address {
  border-left: 3px solid var(--accent-color);
  padding-left: 1rem;
  font-weight: 500;
}

body[data-theme="day"] address .address-number {
  color: white;
  font-weight: 600;
}

body[data-theme="day"] .location-card h3 {
  color: var(--accent-color);
  border-bottom: 2px dashed var(--accent-bg-color);
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* Night mode specific styles */
body[data-theme="night"] .hours-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

body[data-theme="night"] .day {
  color: var(--primary-color);
}

body[data-theme="night"] .hours {
  color: var(--accent-color);
}

body[data-theme="night"] .contact-info strong {
  color: var(--primary-color);
  font-weight: 700;
}

body[data-theme="night"] .contact-info .phone-number {
  color: var(--primary-color);
  font-weight: 700;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
  border: 1px solid var(--primary-color);
}

body[data-theme="night"] address .address-number {
  color: white;
  font-weight: 600;
}

/* Responsive styles */
@media (max-width: 768px) {
  .visit-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}
