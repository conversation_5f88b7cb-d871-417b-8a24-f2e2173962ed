import { useContext } from 'react';
import { ThemeContext } from '../context/ThemeContext';
import { useBooking } from '../context/BookingContext';
import { dayContent, nightContent } from '../data/content';
import './Events.css';

const Events = () => {
  const { mode } = useContext(ThemeContext);
  const { openBookingModal } = useBooking();
  const content = mode === 'day' ? dayContent : nightContent;

  return (
    <section id="events" className="events">
      <div className="container">
        <h2 className="text-center">{content.events.title}</h2>
        <p className="events-subtitle text-center mb-3">{content.events.subtitle}</p>

        <div className="events-list">
          {content.events.list.map((event, index) => (
            <div key={index} className="event-card">
              <div className="event-details">
                <h3 className="event-title">{event.title}</h3>
                <p className="event-date">{event.date}</p>
                <p className="event-time">{event.time}</p>
                <p className="event-description">{event.description}</p>
                <button
                  className="btn btn-primary book-btn"
                  onClick={() => openBookingModal(index)}
                >
                  Book Now
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Events;
