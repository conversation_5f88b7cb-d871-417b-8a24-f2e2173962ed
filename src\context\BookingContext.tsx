import { createContext, useState, useContext } from 'react';
import type { ReactNode } from 'react';
import type { BookingFormData, Booking, BookingState } from '../types/booking';
import { sendBookingEmails } from '../services/emailService';

interface BookingContextType {
  bookingState: BookingState;
  openBookingModal: (eventId: number) => void;
  closeBookingModal: () => void;
  submitBooking: (formData: BookingFormData, eventName: string) => Promise<void>;
  resetBookingForm: () => void;
}

const initialState: BookingState = {
  bookings: [],
  isModalOpen: false,
  selectedEventId: null,
  isSubmitting: false,
  isSubmitted: false,
  error: null
};

export const BookingContext = createContext<BookingContextType>({
  bookingState: initialState,
  openBookingModal: () => {},
  closeBookingModal: () => {},
  submitBooking: async () => {},
  resetBookingForm: () => {}
});

interface BookingProviderProps {
  children: ReactNode;
}

export const BookingProvider = ({ children }: BookingProviderProps) => {
  const [bookingState, setBookingState] = useState<BookingState>(initialState);

  const openBookingModal = (eventId: number) => {
    setBookingState(prev => ({
      ...prev,
      isModalOpen: true,
      selectedEventId: eventId,
      isSubmitted: false,
      error: null
    }));
  };

  const closeBookingModal = () => {
    setBookingState(prev => ({
      ...prev,
      isModalOpen: false,
      selectedEventId: null
    }));
  };

  const resetBookingForm = () => {
    setBookingState(prev => ({
      ...prev,
      isSubmitted: false,
      error: null
    }));
  };

  const submitBooking = async (formData: BookingFormData, eventName: string): Promise<void> => {
    setBookingState(prev => ({
      ...prev,
      isSubmitting: true,
      error: null
    }));

    try {
      // Create a new booking with a unique ID
      const bookingId = `booking-${Date.now()}`;
      const newBooking: Booking = {
        ...formData,
        id: bookingId,
        createdAt: new Date().toISOString(),
        status: 'confirmed'
      };

      // Prepare data for email service
      const emailData = {
        id: bookingId,
        eventName: eventName,
        date: formData.date,
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        numberOfPeople: formData.numberOfPeople,
        specialRequests: formData.specialRequests
      };

      // Send confirmation emails
      await sendBookingEmails(emailData);

      // Add the new booking to the state
      setBookingState(prev => ({
        ...prev,
        bookings: [...prev.bookings, newBooking],
        isSubmitting: false,
        isSubmitted: true
      }));
    } catch (error) {
      console.error('Booking submission error:', error);
      setBookingState(prev => ({
        ...prev,
        isSubmitting: false,
        error: 'Failed to submit booking. Please try again.'
      }));
    }
  };

  return (
    <BookingContext.Provider
      value={{
        bookingState,
        openBookingModal,
        closeBookingModal,
        submitBooking,
        resetBookingForm
      }}
    >
      {children}
    </BookingContext.Provider>
  );
};

// Custom hook for using the booking context
export const useBooking = () => useContext(BookingContext);
